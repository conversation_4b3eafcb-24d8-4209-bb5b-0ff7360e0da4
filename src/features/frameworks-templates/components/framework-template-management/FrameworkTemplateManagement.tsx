'use client';

import { PlusIcon } from '@/shared/icons';
import { SearchBox } from '../SearchBox';
import TableManagement from './table-list/TableManagement';
import TemplateForm from './modals/TemplatesForm';
import { useModal } from '@/shared/hooks/useModal';
import { useFrameworkFilters } from '../../hooks/useFrameworkFilters';
import { useFrameworkGet } from '../../hooks/useFrameworkGet';

const FrameworkAndTemplateManagement: React.FC = () => {
  const { isOpen, openModal, closeModal } = useModal();
  const { apiFilters } = useFrameworkFilters();
  const { isLoading } = useFrameworkGet(apiFilters);

  return (
    <div className="pt-6">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-lg mb-0">Framework & templates</h3>
        <PlusIcon onClick={openModal} className="h-6 w-6 cursor-pointer" />
      </div>

      <div className="mt-3">
        <SearchBox placeholder="Search framework" isLoading={isLoading} />
      </div>

      <div className="overflow-auto">
        <div className="my-2">
          <TableManagement />
        </div>
      </div>

      <TemplateForm isOpen={isOpen} closeModal={closeModal} />
    </div>
  );
};

export default FrameworkAndTemplateManagement;
