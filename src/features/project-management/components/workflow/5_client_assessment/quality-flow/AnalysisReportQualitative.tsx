import type { IFileResponse } from '@/shared/types/global';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { Button } from '@/shared/components/ui/button';
import { CircleCheckIcon } from '@/shared/icons';
import { useParams } from 'next/navigation';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { EQuantitative } from '@/features/project-management/types/questionnaire';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import FileUpload from '../../initial-screening-form/FileUpload';
import QuestionnaireAnalysis from '../../discovery-questionnaire/QuestionnaireAnalysis';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import type { ScoringReportDataType } from '@/features/project-management/types/project';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';

type AnalysisReportQualitativeType = {
  summary: string;
  templates: IFileResponse[];
  report: string;
  id: string;
  stepId: string;
  isAnalysis: boolean;
  isFinish: boolean;
  evaluationFramework: string;
  fileUpload: IFileResponse[];
  idQuality?: string;
  idScoring?: string;
  setIsAnalysis: (status: boolean) => void;
  setScoringData: (data: ScoringReportDataType | null) => void;
};

const AnalysisReportQualitative: React.FC<AnalysisReportQualitativeType> = ({
  summary,
  templates,
  report,
  id,
  stepId,
  isAnalysis,
  isFinish,
  evaluationFramework,
  fileUpload,
  idQuality,
  idScoring,
  setIsAnalysis,
  setScoringData,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [reportData, setReportData] = useState<string>(() => report);

  const [files, setFiles] = useState<IFileResponse[]>(() => [...fileUpload]);

  const [initialFile, _setInitialFile] = useState<IFileResponse[]>(() => [...fileUpload]);

  const [isSaved, _setIsSaved] = useState(true);

  const [isShowModal, setIsShowModal] = useState(false);

  const { registerStep, clearStep } = useDirty();

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  const titleConfirm = 'Confirm Changes';

  const descriptionConfirm = 'Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step';

  const params = useParams<{ id: string }>();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const abortControllerRef = useRef<AbortController | null>(null);

  const saveDataFromAI = async (markdown: string, status: EStatusTask) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 7,
          type: EQuantitative.ANALYSIS,
          infos: [
            { form: markdown, status, isFinish: true },
          ],
        },

      ],
    };

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);

    const isChangedFiled = !compareObjectArray(initialFile, uploadedFiles);
    _setIsSaved(!(isChangedFiled));

    registerStep(stepId, () => (isChangedFiled));
  }, []);

  const saveFileUpload = async () => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 0,
          type: EQuantitative.FILES,
          infos: [{
            files: files.map(file => ({
              ...file,
              file: file.key,
              name: file.originalname,
              type: file.mimeType,
              id: file._id,
            })),
          }],
        },
      ],
    };

    await updateQuestionAnswer(payload, stepId);
  };

  const getScoringContent = async (data: string) => {
    const payload = {
      project_id: params.id,
      evaluation_framework: evaluationFramework,
      content_to_score: data,
    };
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SCORING_CONTENT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const evaluationReport = res.data.result.evaluate_report;
      const evaluationScore = res.data.result.score;

      const data = { report: evaluationReport, score: evaluationScore };
      const payloadScoring = {
        formStepId: id,
        stepInfos: [
          {
            type: EQuantitative.SCORING,
            order: 8,
            infos: [{ value: data }],
          },
        ],
      };

      await updateQuestionAnswer(payloadScoring, stepId);

      setScoringData(data);
    } catch (error: any) {
      console.log(error);
    }
  };

  const isChanged = useMemo(() => {
    if (initialFile.length === 0 && files.length === 0) {
      return true;
    }
    return !compareObjectArray(initialFile, files);
  }, [initialFile, files]);

  const saveDataInfos = async () => {
    await saveFileUpload();
    const payload = {
      project_id: params.id,
      questionnaire_summary: summary,
      research_template_url: [...getFile(templates.length ? templates : [])],
      additional_info_url: [...getFile(files)],
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.REPORT_QUALITY }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const data = res.data.result;
      await getScoringContent(data);
      setReportData(data);
      setIsLoading(false);
      saveDataFromAI(data, EStatusTask.IN_PROGRESS);
    } catch (error) {
      console.error(error);
    }
  };

  const handleGetAnalysis = async () => {
    const isChanged = !compareObjectArray(initialFile, files);

    clearStep(stepId);
    _setIsSaved(false);
    if (isChanged && reportData) {
      setIsShowModal(true);
      return;
    }

    if (!isChanged && reportData) {
      return;
    }

    setIsLoading(true);
    setIsAnalysis(true);
    await saveDataInfos();
  };

  const handleConfirmPopUp = async () => {
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: [idQuality ?? '', idScoring ?? ''],
      select: 'all',
      isGenerate: true,
    });
    saveDataInfos ();
    setIsLoading(true);
    setReportData('');
    clearStep(stepId);
    setIsAnalysis(true);
    setIsShowModal(false);
    setScoringData(null);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleViewAnalysis = () => {
    setFiles(initialFile);
    setIsAnalysis(true);
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const onCompleteMarkdown = (markdown: string) => {
    clearStep(stepId);
    saveDataFromAI(markdown, EStatusTask.COMPLETED);
  };

  return (
    <>
      { isLoading
        ? (<ProjectCardSkeleton />)
        : !isAnalysis
            ? (
          // <div className="w-full mt-4 bg-gray-50 rounded-xl border border-bg-gray-100 flex gap-3 flex-col items-center justify-between py-12">
          //   <div>
          //     Generate analysis for this questionnaire?
          //   </div>

          //   <div className="text-xs text-gray-500">
          //     Click Confirm to start the analysis generation for this questionnaire.
          //   </div>

                // </div>
                <>
                  <FileUpload onFilesChange={handleFilesChange} initialFile={initialFile} />
                  <div className="flex gap-2 w-full items-center justify-center">
                    {reportData && (
                      <Button variant="outline" onClick={handleViewAnalysis} className="mt-4">
                        View Analysis
                      </Button>
                    )}

                    <Button disabled={!isChanged} onClick={handleGetAnalysis} className="mt-4">
                      <CircleCheckIcon className="h-5 w-5" />
                      Confirm
                    </Button>
                  </div>
                </>
              )
            : (
                <QuestionnaireAnalysis
                  stepId={stepId}
                  data={reportData}
                  isHiddenBackButton={true}
                  isHiddenApproveButton={isFinish}
                  isFinish={isFinish}
                  onBack={() => {}}
                  onSubmit={onCompleteMarkdown}
                />
              )}

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titleConfirm}
        description={descriptionConfirm}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText="Continue"
        cancelText="Cancel"
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>
  );
};

export default AnalysisReportQualitative;
