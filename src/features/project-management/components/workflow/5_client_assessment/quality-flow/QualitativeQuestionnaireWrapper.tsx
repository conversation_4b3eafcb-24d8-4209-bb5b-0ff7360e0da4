import { useEffect, useRef, useState } from 'react';
import Header<PERSON>esearch from '../HeaderResearch';
import { EQuantitative } from '@/features/project-management/types/questionnaire';
import type { OptionChangeViewType } from '@/features/project-management/types/questionnaire';
import type { IFileResponse } from '@/shared/types/global';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { documentFileUpload, ScoringReportDataType } from '@/features/project-management/types/project';
import { useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { useParams } from 'next/navigation';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import QuestionnaireAnalysis from '../../discovery-questionnaire/QuestionnaireAnalysis';
import AnalysisReportQualitative from './AnalysisReportQualitative';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useQueryClient } from '@tanstack/react-query';

type QuestionViewType = 'questionnaire' | 'analysis';

type QualityQuestionType = {
  data: any[];
  stepId: string;
  id: string;
  templates: IFileResponse[];
  evaluationFramework: string;
  onBackDashboard: () => void;
  onOpenDetailScore: (data: string) => void;
};

const QualitativeQuestionnaireWrapper: React.FC<QualityQuestionType> = ({
  data,
  stepId,
  id,
  templates,
  evaluationFramework,
  onBackDashboard,
  onOpenDetailScore,
}) => {
  const toggleOptions = [
    { id: 'questionnaire' as QuestionViewType, label: 'Questionnaire' },

  ];

  const summarizedOption = { id: 'analysis' as QuestionViewType, label: 'Analysis' };

  const [options, setOptions] = useState<OptionChangeViewType[]>(toggleOptions);

  const [selectedType, setSelectedType] = useState<QuestionViewType>('questionnaire');

  const [quality, setQuality] = useState<string>('');

  const [statusData, setStatusData] = useState<boolean>(false);

  const [statusAnalysisData, setStatusAnalysisData] = useState<boolean>(false);

  const [isAnalysis, setIsAnalysis] = useState<boolean>(false);

  const [isLoading, setIsLoading] = useState(true);

  const [isFirstCall, setIsFirstCall] = useState<boolean>(false);

  const [scoringData, setScoringData] = useState<ScoringReportDataType | null>(null);

  const [isReGenData, setIsReGenData] = useState<boolean>(false);

  const [analysisBrief, setAnalysisBrief] = useState<string>('');

  const [idsInForm, setIdsInForm] = useState<string[]>([]);

  const [idQuality, setIdQuality] = useState<string>('');

  const [idScoring, setIdScoring] = useState<string>('');

  const [filesUpload, setFilesUpload] = useState<IFileResponse[]>([]);

  const [report, setReport] = useState<string>('');

  const params = useParams<{ id: string }>();

  const workflow = useWorkflowTasks();

  const idSOW = workflow[2]?.steps[1]?.id;

  const { data: dataSOW } = useGetInfoDetail<any, documentFileUpload>(idSOW ?? '');

  const abortControllerRef = useRef<AbortController | null>(null);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const queryClient = useQueryClient();

  const handleSelectType = (option: OptionChangeViewType) => {
    setSelectedType(option.id as QuestionViewType);
  };

  useEffect(() => {
    if (dataSOW && dataSOW.stepInfo.length) {
      const brief = dataSOW?.stepInfo[0]?.infos[0]?.value ?? '';

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setAnalysisBrief(brief);
    }
  }, [dataSOW]);

  const saveDataFromAI = async (markdown: string, status: EStatusTask) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 6,
          type: EQuantitative.QUESTIONNAIRE,
          infos: [
            { value: markdown, status },
          ],
        },

      ],
    };

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  const getDataQuality = async () => {
    if (isFirstCall) {
      return;
    }

    if ((!analysisBrief)) {
      return;
    }

    setIsFirstCall(true);
    const payload = {
      project_id: params.id,
      brief_analysis: analysisBrief,
      questionnaire_template_url: [{
        key: 'https://minastik-store.s3.ap-northeast-1.amazonaws.com/templates/3.+Questionnaire_BrandBeatScore-Quali+.docx',
      }],
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.QUALITY }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const quality = res.data.result;

      setIsLoading(false);
      if (quality) {
        setQuality(quality);
      }

      if (!quality) {
        return;
      }
      saveDataFromAI(quality, EStatusTask.IN_PROGRESS);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (!data.length) {
      getDataQuality();
    } else {
      const quality = data.find(d => d.type === EQuantitative.QUESTIONNAIRE);
      if (quality) {
        const markdown = quality.infos[0].value;
        const status = quality.infos[0].status;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setQuality(markdown);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsLoading(false);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setStatusData(status === EStatusTask.COMPLETED);
        if (status === EStatusTask.COMPLETED) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setOptions([...toggleOptions, summarizedOption]);
        }
      }

      const report = data.find(d => d.type === EQuantitative.ANALYSIS);
      const files = data.find(d => d.type === EQuantitative.FILES);
      if (report) {
        const markdown = report.infos[0].form;
        const status = report.infos[0]?.status;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdQuality(report.id);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setReport(markdown);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsAnalysis(true);

        if (status) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setStatusAnalysisData(status === EStatusTask.COMPLETED);
        } else {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setStatusAnalysisData(false);
        }
      }

      if (files) {
        const filesUpload = files.infos[0].files;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setFilesUpload(filesUpload);
      }

      const scoringData = data.find(d => d.type === EQuantitative.SCORING);

      if (scoringData) {
        const data = scoringData.infos[0].value;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdScoring(scoringData.id);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setScoringData(data);
      }

      const isGenInform = data.some(d => d.isGenerate);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsReGenData(isGenInform);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIdsInForm(data.map(d => d.id));
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, analysisBrief]);

  const onSubmit = () => {};

  const onComplete = () => {};

  const onCompleteMarkdown = (data: string) => {
    saveDataFromAI(data, EStatusTask.COMPLETED);
  };

  const handleReGen = async () => {
    setIsLoading(true);
    setIsReGenData(false);
    setSelectedType('questionnaire');
    setQuality('');
    setScoringData(null);
    setStatusAnalysisData(false);
    setFilesUpload([]);
    setOptions([...toggleOptions]);
    setReport('');
    setIsAnalysis(false);
    setIdQuality('');
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: idsInForm,
      select: 'all',
      isGenerate: true,
    });

    queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });
  };

  return (
    <>
      <div className="sticky top-[70px] bg-white z-100 pb-2">
        <HeaderResearch
          textCopy={quality}
          // isViewCopyBox={selectedType === 'questionnaire' && !!quality && statusData}
          isViewCopyBox={false}
          header="Discovery Questionnaire"
          description="A set of key questions to understand the client’s context, goals, and challenges."
          options={options}
          selectedType={selectedType}
          isHiddenBackButton={selectedType === 'questionnaire' || (selectedType === 'analysis' && !isAnalysis)}
          handleSelectedType={handleSelectType}
          onClickApprove={onSubmit}
          onSaveAndNextStep={onComplete}
          onBackDashboard={onBackDashboard}
          onBackUploadFile={() => setIsAnalysis(false)}
          onOpenDetailScore={onOpenDetailScore}
          isScoringAI={selectedType === 'analysis' && !!scoringData}
          dataScoring={scoringData}
          isShowButtonReGen={isReGenData}
          onReGen={handleReGen}
        />
      </div>

      {isLoading
        ? (
            <div className="mt-4">
              <ProjectCardSkeleton />
            </div>
          )
        : selectedType === 'questionnaire'
          ? (
              <div className="mt-4">
                <QuestionnaireAnalysis
                  stepId={stepId}
                  data={quality}
                  isHiddenBackButton={true}
                  isHiddenApproveButton={statusData}
                  isFinish={statusData}
                  onBack={() => {}}
                  onSubmit={onCompleteMarkdown}
                />
              </div>
            )
          : (
              <div className="mt-4">
                <AnalysisReportQualitative
                  summary={quality}
                  templates={templates}
                  report={report}
                  id={id}
                  stepId={stepId}
                  idQuality={idQuality}
                  idScoring={idScoring}
                  isAnalysis={isAnalysis}
                  evaluationFramework={evaluationFramework}
                  isFinish={statusAnalysisData}
                  fileUpload={filesUpload}
                  setIsAnalysis={setIsAnalysis}
                  setScoringData={setScoringData}
                />
              </div>
            )}
    </>
  );
};

export default QualitativeQuestionnaireWrapper;
