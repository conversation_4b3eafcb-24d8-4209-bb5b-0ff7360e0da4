import { useEffect, useRef, useState } from 'react';
import <PERSON><PERSON><PERSON>esearch from '../HeaderResearch';
import { EQuantitative } from '@/features/project-management/types/questionnaire';
import type { OptionChangeViewType } from '@/features/project-management/types/questionnaire';
import QuantityQuestionnaire from '../../questionnaire/QuanityQuestionnaire';
import type { QuestionnaireFormTypeRef } from '@/features/questionnaire/components/layouts/QuestionnaireForm';
import type { QuestionnaireResponse } from '@/features/questionnaire/types/questionnaire';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { updateNameFieldForQuestionnaire } from '@/features/project-management/utils/workflowUtils';
import { useParams } from 'next/navigation';
import { useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { documentFileUpload, ScoringReportDataType } from '@/features/project-management/types/project';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import ChartSurveyWrapper from '../../discovery-questionnaire/quantitative-questionnaire/chart-survey/ChartSurveyWrapper';
import AnalysisReportQuantitative from './AnalysisReportQuantitative';
import type { IFileResponse } from '@/shared/types/global';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useQueryClient } from '@tanstack/react-query';

type QuestionViewType = 'questionnaire' | 'summary' | 'analysis';

type QuestionFormType = {
  data: any[];
  stepId: string;
  id: string;
  templates: IFileResponse[];
  evaluationFramework: string;
  onBackDashboard: () => void;
  onOpenDetailScore: (data: string) => void;
};

const QuestionFormWrapper: React.FC<QuestionFormType> = ({
  data,
  stepId,
  id,
  templates,
  evaluationFramework,
  onOpenDetailScore,
  onBackDashboard,
}) => {
  const toggleOptions = [
    { id: 'questionnaire' as QuestionViewType, label: 'Questionnaire' },

  ];

  const extendOptions = [
    { id: 'summary' as QuestionViewType, label: 'Answer Summary' },
    { id: 'analysis' as QuestionViewType, label: 'Analysis' },
  ];

  const [options, setOptions] = useState<OptionChangeViewType[]>(() => toggleOptions);

  const [selectedType, setSelectedType] = useState<QuestionViewType>('questionnaire');

  const [quantity, setQuantity] = useState<QuestionnaireResponse | null>(null);

  const [analysisBrief, setAnalysisBrief] = useState<string>('');

  const [dataAnalysis, setDataAnalysis] = useState<string>('');

  const [fileAnalysis, setFileAnalysis] = useState<IFileResponse[]>([]);

  const [isAnalysis, setIsAnalysis] = useState<boolean>(false);

  const [isFinish, setIsFinish] = useState<boolean>(false);

  const [isLoading, setIsLoading] = useState(true);

  const [isFirstCall, setIsFirstCall] = useState<boolean>(false);

  const [scoringData, setScoringData] = useState<ScoringReportDataType | null>(null);

  const [isReGenData, setIsReGenData] = useState<boolean>(false);

  const [textCopyQuestionnaire, setTextCopyQuestionnaire] = useState<string>('');

  const [idQuestionnaire, setIdQuestionnaire] = useState<string>('');

  const [idsInForm, setIdsInForm] = useState<string[]>([]);

  const [idAnalysis, setIdAnalysis] = useState<string>('');

  const [idScoring, setIdScoring] = useState<string>('');

  const form = useRef<QuestionnaireFormTypeRef>(null);

  const params = useParams<{ id: string }>();

  const workflow = useWorkflowTasks();

  const idSOW = workflow[2]?.steps[1]?.id;

  const { data: dataSOW } = useGetInfoDetail<any, documentFileUpload>(idSOW ?? '');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const queryClient = useQueryClient();

  const abortControllerRef = useRef<AbortController | null>(null);

  const handleSelectType = (option: OptionChangeViewType) => {
    setSelectedType(option.id as QuestionViewType);
  };

  useEffect(() => {
    if (dataSOW && dataSOW.stepInfo.length) {
      const brief = dataSOW?.stepInfo[0]?.infos[0]?.value ?? '';

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setAnalysisBrief(brief);
    }
  }, [dataSOW]);

  const saveDataFromAI = async (form: QuestionnaireResponse) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 3,
          type: EQuantitative.QUESTIONNAIRE,
          infos: [
            { form },
          ],
        },

      ],
    };

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  const getDataQuestionnaire = async () => {
    if (isFirstCall) {
      return;
    }

    if ((!analysisBrief)) {
      return;
    }
    setIsFirstCall(true);
    const payload = {
      project_id: params.id,
      brief_analysis: analysisBrief,
      questionnaire_template_url: [{
        key: 'https://minastik-store.s3.ap-northeast-1.amazonaws.com/templates/Questionaire+Template.docx',
      }],
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.QUANTITY }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();

      const quantity = res.data.result?.data;

      setTextCopyQuestionnaire(`${baseUrl}/questionnaire?id=${stepId}`);

      setIsLoading(false);
      if (quantity) {
        setQuantity(updateNameFieldForQuestionnaire(quantity));
        setIdQuestionnaire(quantity.id ?? '');
        setOptions([...toggleOptions, ...extendOptions]);
      }

      if (!quantity) {
        return;
      }
      saveDataFromAI(quantity);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (!data.length) {
      getDataQuestionnaire();
    } else {
      const dataQuestionnaire = data.find(d => d.type === EQuantitative.QUESTIONNAIRE);
      if (dataQuestionnaire) {
        const data = dataQuestionnaire.infos[0].form;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setOptions([...toggleOptions, ...extendOptions]);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdQuestionnaire(data.id ?? '');
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setQuantity(updateNameFieldForQuestionnaire(data));
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsLoading(false);
      }
      const dataAnalysis = data.find(d => d.type === EQuantitative.ANALYSIS);
      if (dataAnalysis) {
        const data = dataAnalysis.infos[0].form;
        const status = dataAnalysis.infos[0]?.status;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdAnalysis(dataAnalysis.id);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setDataAnalysis(data);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsAnalysis(true);

        const baseUrl = window.location.origin;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setTextCopyQuestionnaire(`${baseUrl}/questionnaire?id=${stepId}`);

        if (status) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setIsFinish(status === EStatusTask.COMPLETED);
        }
      }

      const fileAnalysis = data.find(d => d.type === EQuantitative.FILES);
      if (fileAnalysis) {
        const data = fileAnalysis.infos[0].files;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setFileAnalysis(data ?? [] as IFileResponse[]);
      }

      const scoringData = data.find(d => d.type === EQuantitative.SCORING);

      if (scoringData) {
        const data = scoringData.infos[0].value;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdScoring(scoringData.id);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setScoringData(data);
      }

      const isGenInform = data.some(d => d.isGenerate);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsReGenData(isGenInform);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIdsInForm(data.map(d => d.id));
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, analysisBrief]);

  const onSubmit = () => {};

  const onComplete = () => {};

  const handleReGen = async () => {
    setIsLoading(true);
    setIsReGenData(false);
    setSelectedType('questionnaire');
    setQuantity(null);
    setOptions([...toggleOptions]);
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: idsInForm,
      select: 'all',
      isGenerate: true,
    });

    queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });
  };

  return (
    <>
      <div className="sticky top-[70px] bg-white z-100 pb-2">
        <HeaderResearch
          textCopy={textCopyQuestionnaire}
          isViewCopyBox={selectedType === 'questionnaire' && !!quantity}
          // isViewCopyBox={false}
          header="Discovery Questionnaire"
          description="A set of key questions to understand the client’s context, goals, and challenges."
          options={options}
          selectedType={selectedType}
          isHiddenBackButton={!(selectedType === 'analysis') || (selectedType === 'analysis' && !isAnalysis)}
          handleSelectedType={handleSelectType}
          onClickApprove={onSubmit}
          onSaveAndNextStep={onComplete}
          onBackDashboard={onBackDashboard}
          onBackUploadFile={() => setIsAnalysis(false)}
          onOpenDetailScore={onOpenDetailScore}
          isScoringAI={selectedType === 'analysis' && !!scoringData}
          dataScoring={scoringData}
          isShowButtonReGen={isReGenData}
          onReGen={handleReGen}
        />
      </div>

      {isLoading
        ? (
            <div className="mt-4">
              <ProjectCardSkeleton />
            </div>
          )
        : (selectedType === 'questionnaire'
            ? (
                <div className="mt-4">
                  <QuantityQuestionnaire ref={form} questionnaire={quantity} />
                </div>
              )
            : selectedType === 'summary'
              ? (
                  <div className="mt-4">
                    <ChartSurveyWrapper idQuestionnaire={idQuestionnaire} />
                  </div>
                )
              : (
                  <div className="mt-4">
                    <AnalysisReportQuantitative
                      idQuestionnaire={idQuestionnaire}
                      fileAnalysis={fileAnalysis}
                      dataAnalysis={dataAnalysis}
                      id={id}
                      stepId={stepId}
                      templates={templates}
                      evaluationFramework={evaluationFramework}
                      isFinish={isFinish}
                      isAnalysis={isAnalysis}
                      setIsAnalysis={setIsAnalysis}
                      setScoringData={setScoringData}
                      idScoring={idScoring}
                      idAnalysis={idAnalysis}
                    />
                  </div>
                ))}

    </>
  );
};

export default QuestionFormWrapper;
