import { useEffect, useState } from 'react';
import TextUploadEditor from './TextUploadEditor';
import TextUploadMarkdown from './TextUploadMarkdown';
import type { IFileResponse } from '@/shared/types/global';

type TextUpLoadType = {
  data: any[];
  templates: IFileResponse[];
  stepId: string;
  id: string;
  evaluationFramework: string;
  onBackDashboard: () => void;
  onBackUploadFile: () => void;
  getMarkdown: (markdown: string) => void;
  onOpenDetailScore: (data: string) => void;
};

const TextUploadWrapper: React.FC<TextUpLoadType> = ({
  data,
  templates,
  id,
  stepId,
  evaluationFramework,
  onBackDashboard,
  onBackUploadFile,
  getMarkdown,
  onOpenDetailScore,
}) => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  useEffect(() => {
    getMarkdown(markdown);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [markdown]);

  return (
    isEditMode
      ? (
          <TextUploadEditor
            markdown={markdown}
            stepId={stepId}
            onChangeEditMode={setIsEditMode}
            onDataChange={setMarkdown}
          />
        )
      : (
          <TextUploadMarkdown
            markdown={markdown}
            data={data}
            templates={templates}
            id={id}
            stepId={stepId}
            evaluationFramework={evaluationFramework}
            setMarkdown={setMarkdown}
            onBackDashboard={onBackDashboard}
            onBackUploadFile={onBackUploadFile}
            setIsEditResearch={setIsEditMode}
            onOpenDetailScore={onOpenDetailScore}
          />
        )
  );
};

export default TextUploadWrapper;
