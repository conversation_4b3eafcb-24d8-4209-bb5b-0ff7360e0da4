'use client';

import { But<PERSON> } from '@/shared/components/ui/button';
import { CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import { useEffect, useRef, useState } from 'react';
import { useCoAgent } from '@copilotkit/react-core';
import type { quotationOfWorkFlow } from '@/features/project-management/types/agent';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import Editor from '@/shared/components/ui/editor/editor';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { useCurrentStep, useCurrentTask, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { EEndpointApiCopilotkit, ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { ETypeFile } from '@/features/project-management/types/project';
import type { ProjectCampaignEnum, stepInfosMarkdownResponse, TemplateFiles } from '@/features/project-management/types/project';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import { useParams } from 'next/navigation';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { htmlToMarkdownVer2, markdownToHtmlVer2 } from '@/shared/components/ui/editor/parser';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { RefreshCcw } from 'lucide-react';

const QuotationRatingGeneration: React.FC = () => {
  const [isShowEditButton, setIsShowEditButton] = useState(false);

  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [campaignSelected, setCampaignSelected] = useState<ProjectCampaignEnum | null>(null);

  const [_templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [isSaved, _setIsSaved] = useState(true);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [isShowButtonReGen, setIsShowButtonReGen] = useState<boolean>(false);

  const titleConfirm = 'Confirm Changes';

  const titleUnSave = 'UnSave Changes';

  const descriptionUnSave = 'The changes made will be lost. Do you want to proceed?';

  const descriptionConfirm = 'Are you sure you want to make this change? Changing the status will result in the deletion of all related information for this step';

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);

  const currentStep = useCurrentStep();

  const currentStepId = currentStep?.id;

  const workflow = useWorkflowTasks();

  // FIXME: update later
  const idSecondStep = workflow[1]?.steps[0]?.id;

  const currentTask = useCurrentTask();

  const { mutateAsync } = useUpdateStatusStep();

  const { registerStep, clearStep } = useDirty();

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const params = useParams<{ id: string }>();

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: 'Unsaved Changes',
    message: 'You have unsaved changes in the form. Are you sure you want to leave?',
  });

  const { state } = useCoAgent<stateRouteAgent<quotationOfWorkFlow>>({
    name: AGENT_ROUTE_NAME,
  });
  const { data: clientUploadData } = useGetInfoDetail<any, any>(idSecondStep ?? '');

  const { data: templates } = useGetListTemplates();

  const { data: quotationResponse } = useGetInfoDetail<stepInfosMarkdownResponse, stepInfosMarkdownResponse>(currentStepId ?? '');

  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    if (clientUploadData?.stepInfo.length && clientUploadData?.stepInfo[0]?.infos?.length) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCampaignSelected(clientUploadData?.stepInfo[0]?.infos[0]?.serviceOption);
    }
  }, [clientUploadData]);

  useEffect(() => {
    if (templates && campaignSelected) {
      const templateSelect = templates.filter(template => template.campaign === campaignSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);
      setTemplateFile(urlOptions);
    }
  }, [templates, campaignSelected]);

  const saveDataFromAI = async (markdown: string) => {
    // convert same format data
    const html = await markdownToHtmlVer2(markdown);
    const markDownConvert = htmlToMarkdownVer2(html);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markDownConvert }],
        },
      ],
    };

    if (currentStepId) {
      await updateQuestionAnswer(payload, currentStepId);
      mutateAsync({ id: currentStepId, status: EStatusTask.IN_PROGRESS });
    }
  };

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };
  const getGenerateData = async (data: any) => {
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.QUOTATION }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const brief = res.data.result;
      saveDataFromAI(brief);
    } catch (error: any) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (quotationResponse && quotationResponse?.stepInfo.length) {
      const markdown = quotationResponse.stepInfo[0]?.infos[0]?.value;
      const isReGen = quotationResponse.stepInfo[0]?.isGenerate ?? false;

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsShowButtonReGen(isReGen);

      updateMarkdownToState(markdown ?? '');
      if (currentStep?.status !== EStatusTask.COMPLETED) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowEditButton(false);
      } else {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowEditButton(true);
      }
    } else {
      if (quotationResponse && quotationResponse?.stepInfoPrevious.length) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowButtonReGen(false);
        const briefFile = quotationResponse?.stepInfoPrevious?.[0]?.infos?.[0]?.value ?? '';

        if (!_templateFile.length) {
          return;
        }
        const data = {
          project_id: params.id,
          sow_analysis: briefFile,
          ..._templateFile.reduce((result, template) => {
            if (template.type === ETypeFile.QUOTATION) {
              result.quotation_template_url = [
                ...(result.quotation_template_url || []),
                ...getFile([template.file]),
              ];
            }

            return result;
          }, {} as any),
        };
        getGenerateData(data);
      }
    }
  }, [quotationResponse, _templateFile]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    const quotationState = state[ENameStateAgentCopilotkit.QUOTATION];
    if (quotationState?.quotation_analysis_output && quotationState.quotation_analysis_process && quotationState.quotation_analysis_process === 'done') {
      updateMarkdownToState(quotationState?.quotation_analysis_output);
    }
  }, [state]);

  const compareMarkdown = (form?: string) => {
    const markdownInitial = quotationResponse?.stepInfo[0]?.infos[0]?.value ?? '';
    const markdownCurrent = markdown;

    return markdownInitial === (form || markdownCurrent);
  };

  const toggleEditMode = () => {
    setIsEditMode(true);
  };

  const handleFinishStep = async (form?: string) => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: form ?? markdown }],
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);
  };

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    if (currentStep.status !== EStatusTask.COMPLETED) {
      handleFinishStep();
      await mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });

      await mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });

      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }
    clearStep(currentStep?.id ?? '');

    completeStep(currentStepId);
  };

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);

    if (!currentStepId) {
      return;
    }
    const isChanged = compareMarkdown(markdown);

    _setIsSaved(isChanged);
    registerStep(currentStepId, () => !isChanged);
  };

  const discardChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      setIsClickUnSaved(true);
      setIsShowModal(true);
      return;
    }
    clearStep(currentStep?.id ?? '');

    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setIsClickUnSaved(false);
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }
    clearStep(currentStep?.id ?? '');

    setMarkdown(form);
    setIsEditMode(false);
  };

  const handleConfirmPopUp = () => {
    if (isClickUnSaved) {
      setForm(markdown);
      setIsEditMode(false);
      setIsShowModal(false);
      return;
    }
    clearStep(currentStep?.id ?? '');

    handleFinishStep(form);
    setMarkdown(form);
    setIsEditMode(false);
    setIsShowModal(false);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleReGen = () => {
    if (!currentStepId) {
      return;
    }
    setIsLoading(true);
    updateStatus(currentStepId, EStatusTask.IN_PROGRESS);
    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);
    updateQuestionAnswer({ stepInfos: [] }, currentStepId);
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6 z-1">
            {isEditMode
              ? (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={discardChange}
                    >
                      Discard Change
                    </Button>

                    <Button
                      type="button"
                      onClick={confirmChange}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Confirm
                    </Button>
                  </>
                )
              : (
                  <>
                    {isShowButtonReGen && (
                      <Button onClick={handleReGen} type="button" variant="outline" className="text-cyan-500 bg-cyan-50">
                        <RefreshCcw className="h-5 w-5 " />
                      </Button>
                    )}
                    {!isShowEditButton && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={toggleEditMode}
                      >
                        <FileEditIcon className="h-5 w-5 " />
                        Edit
                      </Button>
                    )}

                    {/* <Button
                      type="button"
                      variant="outline"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button> */}
                    <Button
                      type="button"
                      onClick={handleSubmit}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  </>
                )}

          </div>

          <div className="mt-6">
            {
              isEditMode
                ? <Editor onChange={handleChangeEditor} value={markdown} />

                : <MarkdownRenderer content={markdown} />
            }
          </div>

          {/* Modal for confirm content */}
          <GuardConfirmationModal
            open={isShowModal}
            onOpenChange={() => {}}
            title={titlePopup}
            description={descriptionPopUp}
            onConfirm={() => handleConfirmPopUp()}
            onCancel={() => handleCancelPopUp()}
            confirmText="Continue"
            cancelText="Cancel"
          />

          {/* Modal for guard */}
          <GuardConfirmationModal
            open={showDialog}
            onOpenChange={() => {}}
            title={title}
            description={message}
            onConfirm={onConfirm}
            onCancel={onCancel}
          />
        </div>
      );
};

export default QuotationRatingGeneration;
