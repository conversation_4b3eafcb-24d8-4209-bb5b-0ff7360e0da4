import type { IOptionsItem } from './mock-option';

export enum EValueModelAI {
  GPT = 'gpt',
  CLAUDE = 'claude',
  GEMINI = 'gemini',
  LLAMA = 'llama',
}

export const ModelAISelection: IOptionsItem[] = [
  {
    label: 'GPT-4.0',
    value: EValueModelAI.GPT,
  },
  {
    label: 'Claude 3 Opus',
    value: EValueModelAI.CLAUDE,
  },
  {
    label: 'Gemini 1.5 Pro',
    value: EValueModelAI.GEMINI,
  },
  {
    label: 'LLaMA 3',
    value: EValueModelAI.LLAMA,
  },
];
